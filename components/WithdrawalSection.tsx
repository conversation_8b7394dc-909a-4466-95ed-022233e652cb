"use client"

import { useState, useTransition } from "react"
import { But<PERSON> } from "@/components/ui/button"

interface WithdrawalSectionProps {
  availableBalance: number
  pendingBalance: number
  userId: string
}

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

export function WithdrawalSection({ availableBalance, pendingBalance }: WithdrawalSectionProps) {
  const [withdrawalAmount, setWithdrawalAmount] = useState("")
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [isPending, startTransition] = useTransition()

  const handleWithdrawal = (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setSuccess("")

    const amountDollars = parseFloat(withdrawalAmount)
    if (isNaN(amountDollars) || amountDollars < 10) {
      setError("Minimum withdrawal amount is $10.00")
      return
    }

    const amountCents = Math.floor(amountDollars * 100)
    if (amountCents > availableBalance) {
      setError("Withdrawal amount exceeds available balance")
      return
    }

    startTransition(async () => {
      try {
      const response = await fetch("/api/withdraw", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount_cents: amountCents,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        setError(data.error || "Failed to process withdrawal")
      } else {
        setSuccess(data.message)
        setWithdrawalAmount("")
        // Refresh the page to update balance
        window.location.reload()
      }
      } catch {
        setError("An unexpected error occurred")
      }
    })
  }

  const quickAmounts = [10, 25, 50, 100].filter(amount => amount * 100 <= availableBalance)

  return (
    <div className="bg-white rounded-xl p-4 sm:p-6 shadow-sm border border-gray-100 w-full max-w-full">
      <h2 className="text-xl font-serif text-gray-800 mb-6">Withdraw Earnings</h2>

      <div className="bg-white rounded-lg p-4 mb-6 border border-gray-200 shadow-sm">
        <div className="space-y-4">
          {/* Available Balance */}
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm font-semibold text-gray-800">Available Balance</span>
              <div className="text-xs text-gray-500 mt-1">Ready to withdraw now</div>
            </div>
            <span className="text-2xl font-bold text-green-600">{formatPrice(availableBalance)}</span>
          </div>

          {/* Pending Balance */}
          {pendingBalance > 0 && (
            <div className="flex items-center justify-between pt-3 border-t border-gray-100">
              <div>
                <span className="text-sm font-semibold text-gray-800">Pending Balance</span>
                <div className="text-xs text-orange-600 mt-1">Processing • Available in 2-7 days</div>
              </div>
              <span className="text-xl font-bold text-orange-600">{formatPrice(pendingBalance)}</span>
            </div>
          )}

          {/* Total Earnings */}
          <div className="flex items-center justify-between pt-3 border-t border-gray-100">
            <div>
              <span className="text-sm font-semibold text-gray-800">Total Earnings</span>
              <div className="text-xs text-gray-500 mt-1">Available + Pending</div>
            </div>
            <span className="text-xl font-bold text-gray-900">{formatPrice(availableBalance + pendingBalance)}</span>
          </div>
        </div>

        {/* Explanation */}
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="text-xs text-blue-800">
            <p className="font-medium mb-2">💡 How Your Balance Works:</p>
            <ul className="space-y-1 text-blue-700">
              <li>• <strong>Available:</strong> Ready for immediate withdrawal (3-5 business days to your bank)</li>
              <li>• <strong>Pending:</strong> Recent payments being processed by Stripe (2-7 days to become available)</li>
              <li>• <strong>All fees already deducted:</strong> Platform fees (5% donations, 20% subscriptions) + Stripe fees (2.9% + $0.30)</li>
            </ul>
          </div>
        </div>
      </div>

      {availableBalance >= 1000 ? (
        <form onSubmit={handleWithdrawal} className="space-y-6">
          <div>
            <label htmlFor="amount" className="block text-sm font-semibold text-gray-700 mb-3">
              💰 How much would you like to withdraw?
            </label>
            <div className="relative">
              <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">$</span>
              <input
                id="amount"
                type="number"
                min="10"
                max={availableBalance / 100}
                step="0.01"
                value={withdrawalAmount}
                onChange={(e) => setWithdrawalAmount(e.target.value)}
                className="w-full pl-10 pr-4 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-lg font-medium"
                placeholder="0.00"
                required
              />
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Minimum: $10.00 • Maximum: {formatPrice(availableBalance)}
            </p>
          </div>

          {quickAmounts.length > 0 && (
            <div>
              <p className="text-sm font-semibold text-gray-700 mb-3">⚡ Quick amounts:</p>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
                {quickAmounts.map((amount) => (
                  <button
                    key={amount}
                    type="button"
                    onClick={() => setWithdrawalAmount(amount.toString())}
                    className="px-4 py-3 text-sm font-medium bg-green-100 text-green-700 rounded-xl hover:bg-green-200 transition-colors border border-green-200"
                  >
                    ${amount}
                  </button>
                ))}
                <button
                  type="button"
                  onClick={() => setWithdrawalAmount((availableBalance / 100).toFixed(2))}
                  className="px-4 py-3 text-sm font-medium bg-purple-100 text-purple-700 rounded-xl hover:bg-purple-200 transition-colors border border-purple-200"
                >
                  All {formatPrice(availableBalance)}
                </button>
              </div>
            </div>
          )}

          {error && (
            <div className="text-red-700 text-sm bg-red-50 p-4 rounded-xl border border-red-200">
              <div className="flex items-center gap-2">
                <span>❌</span>
                <span className="font-medium">{error}</span>
              </div>
            </div>
          )}

          {success && (
            <div className="text-green-700 text-sm bg-green-50 p-4 rounded-xl border border-green-200">
              <div className="flex items-center gap-2">
                <span>✅</span>
                <span className="font-medium">{success}</span>
              </div>
            </div>
          )}

          <Button
            type="submit"
            isLoading={isPending}
            disabled={!withdrawalAmount}
            className="w-full bg-gray-900 text-white hover:bg-gray-800 py-3 font-medium rounded-lg"
          >
            {isPending ? 'Processing...' : `Withdraw ${withdrawalAmount ? `$${withdrawalAmount}` : 'Funds'}`}
          </Button>

          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mt-4">
            <div className="text-xs text-gray-600">
              <p className="font-medium mb-2">Withdrawal Information:</p>
              <ul className="space-y-1">
                <li>• Funds arrive in 3-5 business days</li>
                <li>• No withdrawal fees charged by OnlyDiary</li>
                <li>• Direct deposit to your bank account</li>
                <li>• Balance shown is your actual Stripe Connect balance (all fees already deducted)</li>
              </ul>
            </div>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mt-4">
            <div className="text-xs text-gray-700">
              <p className="font-medium mb-2 text-blue-800">💡 How Your Earnings Work (Transparency):</p>
              <div className="space-y-2">
                <div>
                  <p className="font-medium text-gray-800">For Donations:</p>
                  <ul className="ml-2 space-y-1 text-gray-600">
                    <li>• OnlyDiary takes 5% platform fee</li>
                    <li>• Stripe charges 2.9% + $0.30 processing fee</li>
                    <li>• You receive the remainder (typically 62-92% depending on amount)</li>
                  </ul>
                </div>
                <div>
                  <p className="font-medium text-gray-800">For Subscriptions:</p>
                  <ul className="ml-2 space-y-1 text-gray-600">
                    <li>• OnlyDiary takes 20% platform fee</li>
                    <li>• Stripe charges 2.9% + $0.30 processing fee</li>
                    <li>• You receive the remainder (typically 75-77% depending on amount)</li>
                  </ul>
                </div>
                <div className="pt-2 border-t border-blue-200">
                  <p className="text-gray-600">
                    <span className="font-medium">Example:</span> $10 donation = You get ~$9.02, $1 donation = You get ~$0.62
                  </p>
                  <p className="text-gray-500 mt-1">
                    All fees are automatically deducted before funds reach your balance.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </form>
      ) : (
        <div className="text-center py-8">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">💰</span>
          </div>
          <h3 className="text-lg font-serif text-gray-800 mb-2">
            Keep Writing to Earn More
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            You need at least $10.00 to request a withdrawal.
          </p>
          <p className="text-xs text-gray-500">
            Current balance: {formatPrice(availableBalance)}
          </p>
        </div>
      )}
    </div>
  )
}
