import Stripe from 'stripe'

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set')
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
})

// Platform fee percentages
export const SUBSCRIPTION_FEE_PERCENTAGE = 0.20 // 20% for subscriptions
export const DONATION_FEE_PERCENTAGE = 0.05 // 5% for donations

// Calculate platform fee from total amount
export function calculatePlatformFee(totalAmount: number, paymentType: 'subscription' | 'donation' = 'subscription'): {
  platformFee: number
  writerAmount: number
  stripeFee: number
  netAfterStripe: number
} {
  // Calculate Stripe fees first (2.9% + $0.30)
  const stripeFee = Math.round(totalAmount * 0.029 + 30)
  const netAfterStripe = totalAmount - stripeFee

  // Calculate platform fee from the net amount after Stripe fees
  const feePercentage = paymentType === 'donation' ? DONATION_FEE_PERCENTAGE : SUBSCRIPTION_FEE_PERCENTAGE
  const platformFeeOnNet = Math.round(netAfterStripe * feePercentage)

  // CRITICAL FIX: Application fee must include Stripe fees + platform fee
  // because Stripe Connect deducts application_fee_amount from gross, not net
  const platformFee = stripeFee + platformFeeOnNet
  const writerAmount = totalAmount - platformFee

  return {
    platformFee,
    writerAmount,
    stripeFee,
    netAfterStripe
  }
}

// Calculate exactly what the writer can withdraw (net amount after all fees)
export function calculateAvailableBalance(payments: Array<{ amount_cents: number; kind: string }>, withdrawals: Array<{ amount_cents: number }>): number {
  const totalWithdrawals = withdrawals.reduce((sum, w) => sum + w.amount_cents, 0)

  const totalNetEarnings = payments.reduce((sum, payment) => {
    // Use the same corrected logic as calculatePlatformFee
    const paymentType = payment.kind === 'donation' ? 'donation' : 'subscription'
    const { writerAmount } = calculatePlatformFee(payment.amount_cents, paymentType)

    return sum + writerAmount
  }, 0)

  return Math.max(0, totalNetEarnings - totalWithdrawals)
}

// Minimum amounts (in cents)
export const MIN_SUBSCRIPTION_AMOUNT = 299 // $2.99
export const MAX_SUBSCRIPTION_AMOUNT = 5000 // $50.00
export const MIN_DONATION_AMOUNT = 100 // $1.00
export const MAX_DONATION_AMOUNT = 10000 // $100.00

// Format price for display
export function formatPrice(cents: number): string {
  return `$${(cents / 100).toFixed(2)}`
}
