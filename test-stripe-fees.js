// Test script to verify the Stripe fee calculation fix
// Run with: node test-stripe-fees.js

function calculatePlatformFee(totalAmount, paymentType = 'subscription') {
  const SUBSCRIPTION_FEE_PERCENTAGE = 0.20 // 20% for subscriptions
  const DONATION_FEE_PERCENTAGE = 0.05 // 5% for donations
  
  // Calculate Stripe fees first (2.9% + $0.30)
  const stripeFee = Math.round(totalAmount * 0.029 + 30)
  const netAfterStripe = totalAmount - stripeFee

  // Calculate platform fee from the net amount after Stripe fees
  const feePercentage = paymentType === 'donation' ? DONATION_FEE_PERCENTAGE : SUBSCRIPTION_FEE_PERCENTAGE
  const platformFeeOnNet = Math.round(netAfterStripe * feePercentage)
  
  // CRITICAL FIX: Application fee must include Stripe fees + platform fee
  // because Stripe Connect deducts application_fee_amount from gross, not net
  const platformFee = stripeFee + platformFeeOnNet
  const writerAmount = totalAmount - platformFee

  return {
    platformFee,
    writerAmount,
    stripeFee,
    netAfterStripe,
    platformFeeOnNet
  }
}

console.log('=== STRIPE FEE CALCULATION TEST ===\n')

// Test with your $4.00 donation
const testAmount = 400 // $4.00 in cents
const result = calculatePlatformFee(testAmount, 'donation')

console.log('Test: $4.00 donation')
console.log('Total amount:', `$${testAmount/100}`)
console.log('Stripe fee:', `$${result.stripeFee/100}`)
console.log('Net after Stripe:', `$${result.netAfterStripe/100}`)
console.log('Platform fee on net (5%):', `$${result.platformFeeOnNet/100}`)
console.log('Total application fee:', `$${result.platformFee/100}`)
console.log('Writer receives:', `$${result.writerAmount/100}`)

console.log('\n=== WHAT HAPPENS IN STRIPE CONNECT ===')
console.log('Gross payment:', `$${testAmount/100}`)
console.log('Stripe deducts application fee:', `-$${result.platformFee/100}`)
console.log('Writer receives:', `$${result.writerAmount/100}`)
console.log('Platform receives (application fee):', `$${result.platformFee/100}`)
console.log('Platform owes Stripe (processing fee):', `-$${result.stripeFee/100}`)
console.log('Platform net profit:', `$${(result.platformFee - result.stripeFee)/100}`)

console.log('\n=== VERIFICATION ===')
const platformNet = result.platformFee - result.stripeFee
console.log('Platform should earn 5% of net:', `$${result.platformFeeOnNet/100}`)
console.log('Platform actually earns:', `$${platformNet/100}`)
console.log('Match?', platformNet === result.platformFeeOnNet ? '✅ YES' : '❌ NO')
